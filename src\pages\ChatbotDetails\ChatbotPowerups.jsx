import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import PowerUps from '@/components/Chatbot/PowerUps';
import DButton from '@/components/Global/DButton';
import { COMMON_CLASSNAMES } from '@/constants';
import useCustomizationData from '@/hooks/useCustomization';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import ReactRouterPrompt from 'react-router-prompt';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import compareObjects from '@/helpers/compareObjects';
import DLoading from '@/components/DLoading';
import { updateChatbotPowerUps } from '@/services/customization.service';
import useToast  from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { trackKlaviyoEvent } from '@/services/chatbot.service';
import StyleTag from '@/components/StyleTag';

const ChatbotPowerups = () => {
  let params = useParams();
  const navigate = useNavigate();
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );
  const { addSuccessToast, addErrorToast } = useToast();

  const { customizationData, setChatbotCustomization, savingChanges } =
    useCustomizationData(true, params.id);

  const [tempCustomization, setTempCustomization] = useState(customizationData);
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [errorLeadGenFields, setErrorLeadGenFields] = useState(false);
  const [customUrlError, setCustomUrlError] = useState('');

  const [changedData, setChangedData] = useState({});
  const [dropdownValidationErrors, setDropdownValidationErrors] = useState({});

  const validateDropdowns = () => {
    const errors = {};
    let isValid = true;

    if (tempCustomization.talk_to_live_agent) {
      // Validate availability dropdown - check if any availability option is selected
      const hasAvailabilitySelection =
        tempCustomization.live_agent_always_available ||
        tempCustomization.live_agent_office_hours ||
        tempCustomization.live_agent_custom_schedule;

      if (!hasAvailabilitySelection) {
        errors.availability = 'Please select an availability option';
        isValid = false;
      }

     
      const isShowOnStart = tempCustomization.show_live_agent_on_conversation_begin;

     
      const isShowBasedOnRule = !isShowOnStart;

      if (isShowOnStart) {
        // "Show on start" is selected - this is valid
      } else if (isShowBasedOnRule) {
      
        if (!tempCustomization.show_live_agent_rule || tempCustomization.show_live_agent_rule.trim() === '') {
          errors.handoverRule = 'Rule is required when "Show based on rule" is selected';
          isValid = false;
        }
      } else {
       
        errors.handoverVisibility = 'Please select a visibility option';
        isValid = false;
      }
    }

    setDropdownValidationErrors(errors);
    return isValid;
  };


  const handleSave = async () => {
    setIsSaveLoading(true);

    // Validate dropdown selections
    if (!validateDropdowns()) {
      addErrorToast({
        message: 'Please complete all required fields before saving',
      });
      setIsSaveLoading(false);
      return;
    }

    // Validate lead generation fields
    if(changedData.data_collection && (changedData.data_collection_fields?.length === 0 || !changedData.data_collection_fields)){
      setErrorLeadGenFields(true);
      setIsSaveLoading(false);
      return;
    }


    if(tempCustomization.custom_url_enabled && tempCustomization.custom_url && tempCustomization.custom_url.trim() !== '') {

      const customUrl = tempCustomization.custom_url;
      const cleanUrl = customUrl.replace(/^https?:\/\//, '').trim();
      const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?\.([a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?\.)*[a-zA-Z]{2,}$/;

      const isValidUrl = !cleanUrl.toLowerCase().includes('dante-ai') &&
                        domainPattern.test(cleanUrl) &&
                        !customUrl.includes('://');

      // Only show DNS records validation if the URL is valid
      if(isValidUrl && (!tempCustomization.dns_values || tempCustomization.dns_values.length === 0)) {
        addErrorToast({
          message: 'Please click "Get DNS records" before saving when using a custom URL',
        });
        setIsSaveLoading(false);
        return;
      }
    }

    try{
      console.log('💾 Saving changed data:', changedData);
      const res = await updateChatbotPowerUps(params.id, changedData);
      console.log('📥 Server response:', res.data);
      if(res.status === 200){
        // Track customized-powerup event
        const user = useUserStore.getState().user;
        await trackKlaviyoEvent('customized-powerup', {
          chatbot_id: params.id
        });
        setChatbotCustomization(res.data);
        setTempCustomization(res.data);
        addSuccessToast({
          message: 'Power-ups updated successfully',
        });
        setIsSaveLoading(false);
      }
    }catch(e){
      setIsSaveLoading(false);
      console.log('error', e)
    }finally{
      setIsSaveLoading(false);
      setErrorLeadGenFields(false);
    }
  };

  useEffect(() => {
    setSidebarOpen(false);
    setProgressBar([]);
    setIsInPreviewBubblePage(true);
  }, []);

  useEffect(() => {
    setTempCustomization(customizationData);
  }, [customizationData]);

  useEffect(() => {
    if (!tempCustomization || !customizationData) {
      return;
    }

    const hasUnsavedChanges = !compareObjects(
      customizationData,
      tempCustomization
    );

    setUnsavedChanges(hasUnsavedChanges);
  }, [tempCustomization]);

  if (!customizationData || !tempCustomization?.kb_id) {
    return <DLoading show={true} />;
  }

  return (
    <>
      <LayoutRightSidebar
        RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <StyleTag tag=".bubble" tempCustomizationData={tempCustomization} />
            <Bubble
              type="chatbot"
              config={{
                ...tempCustomization,
                public: true,
                home_tab_enabled: false,
                remove_watermark: true,
              }}
              isPreviewMode={true}
              isInApp={false}
            />
          </div>
        )}
      >
        {() => (
            <LayoutWithButtons
              footer={
                <div className="flex items-center justify-between">
                  <DButton
                    variant="grey"
                    className="!h-12 w-full md:w-auto md:min-w-32 xl:!min-w-52"
                    onClick={() => navigate(`/chatbot/${params.id}`)}
                  >
                    Cancel
                  </DButton>
                  <DButton
                    variant="dark"
                    className="!h-12 w-full md:w-auto md:min-w-32 xl:!min-w-52"
                    onClick={handleSave}
                    loading={isSaveLoading}
                    disabled={compareObjects(
                      customizationData,
                      tempCustomization
                    ) || customUrlError !== ''}
                  >
                    Save
                  </DButton>
                </div>
              }
            >
              <PowerUps
                customizationData={tempCustomization}
                updateCustomizationData={(key, value) =>{
                  setTempCustomization((prev) => ({ ...prev, [key]: value }))
                  setChangedData((prev) => {
                    const newChangedData = { ...prev };

                    // Special handling for availability fields - always include all three boolean flags
                    if (key === 'live_agent_always_available' || key === 'live_agent_office_hours' || key === 'live_agent_custom_schedule') {
                      // Get current temp values for all availability fields
                      const currentTemp = { ...prev };
                      currentTemp[key] = value; // Update with the new value

                      // Always include all three availability flags in changed data
                      newChangedData.live_agent_always_available = key === 'live_agent_always_available' ? value : tempCustomization.live_agent_always_available;
                      newChangedData.live_agent_office_hours = key === 'live_agent_office_hours' ? value : tempCustomization.live_agent_office_hours;
                      newChangedData.live_agent_custom_schedule = key === 'live_agent_custom_schedule' ? value : tempCustomization.live_agent_custom_schedule;

                      console.log(`🔧 Availability field updated ${key}:`, value);
                      console.log('📦 All availability flags in changed data:', {
                        live_agent_always_available: newChangedData.live_agent_always_available,
                        live_agent_office_hours: newChangedData.live_agent_office_hours,
                        live_agent_custom_schedule: newChangedData.live_agent_custom_schedule
                      });
                    } else {
                      // Normal handling for other fields
                      if (customizationData[key] === value) {
                        delete newChangedData[key];
                      } else {
                        newChangedData[key] = value;
                      }
                    }

                    return newChangedData;
                  })
                  // Clear validation errors when user makes changes
                  if (key === 'live_agent_always_available' || key === 'live_agent_office_hours' || key === 'live_agent_custom_schedule') {
                    setDropdownValidationErrors(prev => ({ ...prev, availability: '' }));
                  }
                  if (key === 'show_live_agent_on_conversation_begin' || key === 'show_live_agent_rule') {
                    setDropdownValidationErrors(prev => ({ ...prev, handoverVisibility: '' }));
                  }
                  // Clear rule validation error when user types in the rule field
                  if (key === 'show_live_agent_rule') {
                    setDropdownValidationErrors(prev => ({ ...prev, handoverRule: '' }));
                  }
                }}
                errorLeadGenFields={errorLeadGenFields}
                setErrorLeadGenFields={setErrorLeadGenFields}
                onCustomUrlErrorChange={setCustomUrlError}
                dropdownValidationErrors={dropdownValidationErrors}
              />
              <ReactRouterPrompt when={unsavedChanges}>
                {({ isActive, onConfirm, onCancel }) => (
                  <DConfirmationModal
                    open={isActive}
                    onClose={onCancel}
                    onConfirm={onConfirm}
                    title="Are you sure you want to leave this page?"
                    description="You have unsaved changes. If you leave, you will lose your changes."
                    confirmText="Leave"
                    cancelText="Cancel"
                    variantConfirm="danger"
                  />
                )}
              </ReactRouterPrompt>
            </LayoutWithButtons>
        )}
      </LayoutRightSidebar>
    </>
  );
};

export default ChatbotPowerups;
